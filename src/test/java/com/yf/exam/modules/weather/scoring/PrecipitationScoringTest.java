package com.yf.exam.modules.weather.scoring;

import com.yf.exam.modules.weather.scoring.dto.PrecipitationScoringResult;
import com.yf.exam.modules.weather.scoring.service.PrecipitationAreaScoringService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * 降水落区评分测试
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PrecipitationScoringTest {

    @Autowired
    private PrecipitationAreaScoringService precipitationAreaScoringService;

    @Test
    public void testPrecipitationScoring() {
        try {
            // 创建测试数据
            String actualFilePath = "sample_actual.000";
            String cmaMesoFilePath = "sample_cma_meso.004";
            Map<String, Object> studentAnswer = createTestStudentAnswer();

            // 执行评分
            PrecipitationScoringResult result = precipitationAreaScoringService
                    .calculatePrecipitationScore(actualFilePath, cmaMesoFilePath, studentAnswer);

            // 验证结果
            if (result.isSuccess()) {
                System.out.println("=== 降水落区评分测试成功 ===");
                System.out.println("最终得分：" + result.getFinalScore() + "/40");
                System.out.println("总站点数：" + result.getTotalStations());
                
                if (result.getStudentTSScores() != null) {
                    System.out.println("学生TS评分：");
                    result.getStudentTSScores().forEach((level, score) -> 
                        System.out.println("  " + level + "：" + String.format("%.3f", score)));
                }
                
                System.out.println("\n评分摘要：");
                System.out.println(result.getScoringSummary());
            } else {
                System.err.println("降水落区评分测试失败：" + result.getMessage());
            }

        } catch (Exception e) {
            System.err.println("降水落区评分测试异常：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建测试用的考生答案
     */
    private Map<String, Object> createTestStudentAnswer() {
        Map<String, Object> answer = new HashMap<>();
        Map<String, Object> content = new HashMap<>();
        
        // 小雨区域
        Map<String, Object> smallRainArea = new HashMap<>();
        Map<String, Object> smallRainGeometry = new HashMap<>();
        smallRainGeometry.put("type", "Polygon");
        smallRainGeometry.put("coordinates", Arrays.asList(
            Arrays.asList(
                Arrays.asList(115.0, 39.0),
                Arrays.asList(119.0, 39.0),
                Arrays.asList(119.0, 41.0),
                Arrays.asList(115.0, 41.0),
                Arrays.asList(115.0, 39.0)
            )
        ));
        smallRainArea.put("geometry", smallRainGeometry);
        content.put("小雨", Arrays.asList(smallRainArea));
        
        answer.put("content", content);
        return answer;
    }
}
