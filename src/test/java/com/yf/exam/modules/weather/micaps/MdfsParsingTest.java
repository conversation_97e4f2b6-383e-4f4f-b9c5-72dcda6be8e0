package com.yf.exam.modules.weather.micaps;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * MDFS文件解析测试
 * 测试MICAPS MDFS混合格式文件的解析功能
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MdfsParsingTest {

    @Autowired
    private MicapsDataService micapsDataService;

    @Test
    public void testMdfsFileHeaderParsing() {
        try {
            System.out.println("=== MDFS文件头解析测试 ===");
            
            // 创建测试MDFS文件
            File testFile = createTestMdfsFile();
            System.out.println("创建测试文件: " + testFile.getAbsolutePath());
            
            // 解析文件
            MicapsData data = micapsDataService.parseMicapsFile(testFile.getAbsolutePath());
            
            if (data != null) {
                System.out.println("✅ 文件解析成功!");
                System.out.println("数据类型: " + data.getDataType());
                System.out.println("数据描述: " + data.getDescription());
                
                if (data instanceof MicapsType1Data) {
                    MicapsType1Data stationData = (MicapsType1Data) data;
                    System.out.println("站点数量: " + stationData.getTotalStations());
                    System.out.println("实际解析站点数: " + 
                        (stationData.getStations() != null ? stationData.getStations().size() : 0));
                }
            } else {
                System.err.println("❌ 文件解析失败，返回null");
            }
            
            // 清理测试文件
            testFile.delete();
            
        } catch (Exception e) {
            System.err.println("测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testEncodingDetection() {
        try {
            System.out.println("=== 编码检测测试 ===");
            
            // 测试不同编码的文件头
            String[] encodings = {"GBK", "UTF-8", "GB2312"};
            String headerText = "mdfs 国家站24小时降水_08-08 surface";
            
            for (String encoding : encodings) {
                System.out.println("\n测试编码: " + encoding);
                
                File testFile = createTestFileWithEncoding(headerText, encoding);
                
                try {
                    MicapsData data = micapsDataService.parseMicapsFile(testFile.getAbsolutePath());
                    if (data != null) {
                        System.out.println("✅ " + encoding + " 编码解析成功");
                        System.out.println("描述: " + data.getDescription());
                    } else {
                        System.out.println("❌ " + encoding + " 编码解析失败");
                    }
                } catch (Exception e) {
                    System.out.println("❌ " + encoding + " 编码解析异常: " + e.getMessage());
                }
                
                testFile.delete();
            }
            
        } catch (Exception e) {
            System.err.println("编码测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testBinaryDataHandling() {
        try {
            System.out.println("=== 二进制数据处理测试 ===");
            
            // 创建包含二进制数据的测试文件
            File testFile = createMixedFormatFile();
            System.out.println("创建混合格式测试文件: " + testFile.getAbsolutePath());
            
            // 解析文件
            MicapsData data = micapsDataService.parseMicapsFile(testFile.getAbsolutePath());
            
            if (data != null) {
                System.out.println("✅ 混合格式文件解析成功!");
                System.out.println("数据类型: " + data.getDataType());
                System.out.println("数据描述: " + data.getDescription());
                
                if (data instanceof MicapsType1Data) {
                    MicapsType1Data stationData = (MicapsType1Data) data;
                    System.out.println("解析的站点数: " + 
                        (stationData.getStations() != null ? stationData.getStations().size() : 0));
                    
                    if (stationData.getStations() != null && !stationData.getStations().isEmpty()) {
                        System.out.println("第一个站点信息:");
                        MicapsStation firstStation = stationData.getStations().get(0);
                        System.out.println("  站号: " + firstStation.getStationId());
                        System.out.println("  经度: " + firstStation.getLongitude());
                        System.out.println("  纬度: " + firstStation.getLatitude());
                        System.out.println("  降水: " + firstStation.getPrecipitation6h());
                    }
                }
            } else {
                System.err.println("❌ 混合格式文件解析失败");
            }
            
            // 清理测试文件
            testFile.delete();
            
        } catch (Exception e) {
            System.err.println("二进制数据测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建测试MDFS文件
     */
    private File createTestMdfsFile() throws IOException {
        File tempFile = File.createTempFile("test_mdfs", ".000");
        
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            // 写入GBK编码的文件头
            String header = "mdfs 国家站24小时降水_08-08 surface";
            fos.write(header.getBytes("GBK"));
            fos.write('\n');
            
            // 写入一些模拟的二进制数据
            byte[] binaryData = new byte[100];
            for (int i = 0; i < binaryData.length; i++) {
                binaryData[i] = (byte) (i % 256);
            }
            fos.write(binaryData);
        }
        
        return tempFile;
    }

    /**
     * 创建指定编码的测试文件
     */
    private File createTestFileWithEncoding(String text, String encoding) throws IOException {
        File tempFile = File.createTempFile("test_encoding_" + encoding, ".000");
        
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(text.getBytes(encoding));
            fos.write('\n');
        }
        
        return tempFile;
    }

    /**
     * 创建混合格式文件（文本头 + 二进制数据）
     */
    private File createMixedFormatFile() throws IOException {
        File tempFile = File.createTempFile("test_mixed", ".000");
        
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            // 文本头部（GBK编码）
            String header = "mdfs 国家站24小时降水_08-08 surface";
            fos.write(header.getBytes("GBK"));
            
            // 添加一些填充字符
            fos.write(new byte[100]); // 填充字节
            
            // 模拟二进制站点数据
            // 假设格式：站号(4字节) + 经度(4字节) + 纬度(4字节) + 降水(4字节)
            for (int i = 0; i < 5; i++) {
                // 站号
                fos.write(intToBytes(54511 + i));
                // 经度 (116.0 + i)
                fos.write(floatToBytes(116.0f + i));
                // 纬度 (39.0 + i)
                fos.write(floatToBytes(39.0f + i));
                // 降水量 (5.0 + i)
                fos.write(floatToBytes(5.0f + i));
            }
        }
        
        return tempFile;
    }

    /**
     * 整数转字节数组（小端序）
     */
    private byte[] intToBytes(int value) {
        return new byte[] {
            (byte) (value & 0xFF),
            (byte) ((value >> 8) & 0xFF),
            (byte) ((value >> 16) & 0xFF),
            (byte) ((value >> 24) & 0xFF)
        };
    }

    /**
     * 浮点数转字节数组（小端序）
     */
    private byte[] floatToBytes(float value) {
        int intBits = Float.floatToIntBits(value);
        return intToBytes(intBits);
    }

    @Test
    public void testRealMdfsFile() {
        try {
            System.out.println("=== 真实MDFS文件测试 ===");
            
            // 如果有真实的MDFS文件，可以在这里测试
            String realFilePath = "path/to/real/mdfs/file.000";
            File realFile = new File(realFilePath);
            
            if (realFile.exists()) {
                System.out.println("测试真实文件: " + realFilePath);
                
                MicapsData data = micapsDataService.parseMicapsFile(realFilePath);
                
                if (data != null) {
                    System.out.println("✅ 真实文件解析成功!");
                    System.out.println("数据类型: " + data.getDataType());
                    System.out.println("数据描述: " + data.getDescription());
                } else {
                    System.err.println("❌ 真实文件解析失败");
                }
            } else {
                System.out.println("跳过真实文件测试（文件不存在）");
            }
            
        } catch (Exception e) {
            System.err.println("真实文件测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
