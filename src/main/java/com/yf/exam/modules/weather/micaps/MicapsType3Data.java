package com.yf.exam.modules.weather.micaps;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * MICAPS第三类数据：非规范站点填图数据
 * 主要用于非规范的站点填图，支持单要素填图和等值线绘制
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MicapsType3Data extends MicapsData {
    
    /**
     * 数据说明
     */
    private String description;
    
    /**
     * 年
     */
    private int year;
    
    /**
     * 月
     */
    private int month;
    
    /**
     * 日
     */
    private int day;
    
    /**
     * 时次
     */
    private int hour;
    
    /**
     * 层次（或格式控制标志）
     * -1: 6小时降水量格式
     * -2: 24小时降水量格式  
     * -3: 温度格式
     * 其他: 普通层次
     */
    private int level;
    
    /**
     * 等值线条数
     */
    private int contourCount;
    
    /**
     * 等值线值1
     */
    private double contourValue1;
    
    /**
     * 等值线值2
     */
    private double contourValue2;
    
    /**
     * 平滑系数
     */
    private double smoothFactor;
    
    /**
     * 加粗线值
     */
    private double boldLineValue;
    
    /**
     * 剪切区域边缘线上的点数
     */
    private int clipBoundaryPointCount;
    
    /**
     * 剪切区域边缘线上各点的经纬度
     */
    private List<ClipBoundaryPoint> clipBoundaryPoints;
    
    /**
     * 单站填图要素的个数
     */
    private int elementCount;
    
    /**
     * 总站点数
     */
    private int totalStations;
    
    /**
     * 站点数据列表
     */
    private List<MicapsType3Station> stations;
    
    /**
     * 剪切区域边界点
     */
    @Data
    public static class ClipBoundaryPoint {
        /**
         * 经度
         */
        private double longitude;
        
        /**
         * 纬度
         */
        private double latitude;
        
        public ClipBoundaryPoint(double longitude, double latitude) {
            this.longitude = longitude;
            this.latitude = latitude;
        }
    }
    
    /**
     * 第三类数据站点信息
     */
    @Data
    public static class MicapsType3Station {
        /**
         * 区站号
         */
        private long stationId;
        
        /**
         * 经度
         */
        private double longitude;
        
        /**
         * 纬度
         */
        private double latitude;
        
        /**
         * 拔海高度
         */
        private double elevation;
        
        /**
         * 站点值1（字符串格式）
         */
        private String value1;
        
        /**
         * 站点值2（字符串格式）
         */
        private String value2;
        
        /**
         * 获取数值型的站点值1
         */
        public Double getNumericValue1() {
            try {
                return value1 != null && !value1.trim().isEmpty() ? Double.parseDouble(value1.trim()) : null;
            } catch (NumberFormatException e) {
                return null;
            }
        }
        
        /**
         * 获取数值型的站点值2
         */
        public Double getNumericValue2() {
            try {
                return value2 != null && !value2.trim().isEmpty() ? Double.parseDouble(value2.trim()) : null;
            } catch (NumberFormatException e) {
                return null;
            }
        }
        
        /**
         * 检查站点是否有效
         */
        public boolean isValid() {
            return stationId > 0 && 
                   Math.abs(longitude) <= 180 && 
                   Math.abs(latitude) <= 90 &&
                   (value1 != null || value2 != null);
        }
    }
    
    /**
     * 获取指定区域内的站点
     */
    public List<MicapsType3Station> getStationsInRegion(double minLon, double maxLon, 
                                                        double minLat, double maxLat) {
        if (stations == null) {
            return List.of();
        }
        
        return stations.stream()
                .filter(station -> station.getLongitude() >= minLon && 
                                 station.getLongitude() <= maxLon &&
                                 station.getLatitude() >= minLat && 
                                 station.getLatitude() <= maxLat)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取有有效数值的站点
     */
    public List<MicapsType3Station> getStationsWithValidValues() {
        if (stations == null) {
            return List.of();
        }
        
        return stations.stream()
                .filter(station -> station.getNumericValue1() != null || 
                                 station.getNumericValue2() != null)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取格式化类型描述
     */
    public String getFormatDescription() {
        switch (level) {
            case -1:
                return "6小时降水量格式";
            case -2:
                return "24小时降水量格式";
            case -3:
                return "温度格式";
            default:
                return "层次: " + level;
        }
    }
    
    /**
     * 是否支持等值线绘制
     */
    public boolean supportsContourDrawing() {
        return contourCount > 0;
    }
    
    /**
     * 是否有剪切区域
     */
    public boolean hasClipRegion() {
        return clipBoundaryPointCount > 0 && clipBoundaryPoints != null && !clipBoundaryPoints.isEmpty();
    }
}
