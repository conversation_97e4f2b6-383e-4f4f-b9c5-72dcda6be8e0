package com.yf.exam.modules.weather.micaps;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * MICAPS数据服务类
 * 提供MICAPS数据解析和处理的业务方法
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Slf4j
@Service
public class MicapsDataService {

    @Autowired
    private MdfsBinaryParser binaryParser;
    
    /**
     * 解析MICAPS文件
     * MICAPS MDFS文件是混合格式：文件头是文本格式，数据部分是二进制格式
     *
     * @param filePath 文件路径
     * @return 解析结果
     * @throws IOException 文件读取异常
     */
    public MicapsData parseMicapsFile(String filePath) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new FileNotFoundException("MICAPS文件不存在: " + filePath);
        }

        log.info("开始解析MICAPS MDFS文件: {}, 文件大小: {} bytes", filePath, file.length());

        // MICAPS MDFS文件格式分析：
        // 1. 文件头部分：文本格式（通常是GBK编码）
        // 2. 数据部分：二进制格式
        // 需要分别处理这两个部分

        MdfsFileHeader header = parseMdfsHeader(file);
        log.info("解析文件头成功: 数据类型={}, 描述={}", header.getDataType(), header.getDescription());

        // 根据数据类型调用相应的解析方法
        switch (header.getDataType()) {
            case 1:
                return parseType1MdfsData(file, header);
            case 4:
                return parseType4MdfsData(file, header);
            default:
                throw new IllegalArgumentException("不支持的MICAPS数据类型: " + header.getDataType());
        }
    }

    /**
     * 解析MDFS文件头（文本部分）
     * MDFS文件头通常是固定长度的文本，后面直接跟二进制数据
     *
     * @param file 文件对象
     * @return 文件头信息
     * @throws IOException 读取异常
     */
    private MdfsFileHeader parseMdfsHeader(File file) throws IOException {
        // 尝试多种编码读取文件头
        String[] encodings = {"GBK", "GB2312", "UTF-8"};

        for (String encoding : encodings) {
            try {
                MdfsFileHeader header = tryParseHeaderWithEncoding(file, encoding);
                if (header != null && header.isValid()) {
                    log.info("使用编码 {} 成功解析MDFS文件头: {}", encoding, header.getFormattedInfo());
                    return header;
                }
            } catch (Exception e) {
                log.debug("使用编码 {} 解析文件头失败: {}", encoding, e.getMessage());
            }
        }

        throw new IOException("无法解析MDFS文件头，可能文件格式不正确");
    }

    /**
     * 尝试使用指定编码解析文件头
     */
    private MdfsFileHeader tryParseHeaderWithEncoding(File file, String encoding) throws IOException {
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            // MDFS文件头通常在前256字节内
            int maxHeaderLength = Math.min(256, (int) file.length());
            byte[] headerBytes = new byte[maxHeaderLength];

            raf.seek(0);
            int bytesRead = raf.read(headerBytes);

            if (bytesRead < 10) {
                return null; // 文件太小
            }

            // 尝试将字节转换为文本
            String headerText = new String(headerBytes, 0, bytesRead, encoding);

            // 查找文本部分的结束位置
            int textEndPos = findTextEndPosition(headerBytes, encoding);
            if (textEndPos > 0) {
                // 只取文本部分
                headerText = new String(headerBytes, 0, textEndPos, encoding);
            }

            // 验证是否为有效的MDFS文件头
            if (isValidMdfsHeader(headerText)) {
                MdfsFileHeader header = parseMdfsHeaderContent(headerText, encoding);
                header.setHeaderLength(textEndPos > 0 ? textEndPos : bytesRead);
                header.setDataStartOffset(textEndPos > 0 ? textEndPos : bytesRead);

                log.debug("解析文件头成功: 文本长度={}, 数据开始位置={}",
                    header.getHeaderLength(), header.getDataStartOffset());

                return header;
            }

            return null;
        }
    }

    /**
     * 查找文本部分的结束位置
     * 通过检测连续的非文本字符来判断二进制数据的开始
     */
    private int findTextEndPosition(byte[] bytes, String encoding) {
        try {
            String text = new String(bytes, encoding);

            // 查找第一个换行符后的位置
            int newlinePos = -1;
            for (int i = 0; i < text.length(); i++) {
                char c = text.charAt(i);
                if (c == '\n' || c == '\r') {
                    newlinePos = i;
                    break;
                }
            }

            if (newlinePos > 0) {
                // 检查换行符后是否有更多文本
                int pos = newlinePos + 1;
                if (pos < text.length()) {
                    // 跳过可能的\r\n
                    if (text.charAt(newlinePos) == '\r' && pos < text.length() && text.charAt(pos) == '\n') {
                        pos++;
                    }

                    // 检查后面是否还有可读文本
                    boolean hasMoreText = false;
                    for (int i = pos; i < Math.min(pos + 50, text.length()); i++) {
                        char c = text.charAt(i);
                        if (c >= 32 && c < 127) { // 可打印ASCII字符
                            hasMoreText = true;
                            break;
                        }
                    }

                    if (!hasMoreText) {
                        // 换行符后没有更多文本，认为是二进制数据开始
                        return newlinePos + (text.charAt(newlinePos) == '\r' && pos < text.length() && text.charAt(pos) == '\n' ? 2 : 1);
                    }
                }
            }

            // 如果没有找到明确的分界点，查找连续的非文本字符
            int consecutiveNonText = 0;
            for (int i = 0; i < text.length(); i++) {
                char c = text.charAt(i);
                if (c < 32 && c != '\t' && c != '\r' && c != '\n') {
                    consecutiveNonText++;
                    if (consecutiveNonText >= 3) {
                        // 连续3个非文本字符，认为是二进制数据开始
                        return Math.max(0, i - 2);
                    }
                } else {
                    consecutiveNonText = 0;
                }
            }

            return -1; // 没有找到明确的分界点

        } catch (Exception e) {
            log.debug("查找文本结束位置失败: {}", e.getMessage());
            return -1;
        }
    }

    /**
     * 解析MDFS文件头行
     */
    private MdfsFileHeader parseMdfsHeaderLine(String headerLine, String encoding) {
        // MDFS文件头格式示例：
        // mdfs 国家站24小时降水_08-08 surface ...

        MdfsFileHeader header = new MdfsFileHeader();
        header.setEncoding(encoding);
        header.setRawHeader(headerLine);

        // 查找数据类型（通常在描述信息后面）
        // 由于MDFS格式比较复杂，我们需要更智能的解析方式

        if (headerLine.contains("站") || headerLine.contains("surface")) {
            // 站点数据，通常是第一类数据
            header.setDataType(1);
            header.setDescription(extractDescription(headerLine));
        } else if (headerLine.contains("格点") || headerLine.contains("grid")) {
            // 格点数据，通常是第四类数据
            header.setDataType(4);
            header.setDescription(extractDescription(headerLine));
        } else {
            // 尝试从文件头中提取数据类型
            String[] parts = headerLine.trim().split("\\s+");
            for (String part : parts) {
                try {
                    int type = Integer.parseInt(part);
                    if (type == 1 || type == 4) {
                        header.setDataType(type);
                        break;
                    }
                } catch (NumberFormatException ignored) {
                    // 继续尝试下一个部分
                }
            }

            if (header.getDataType() == 0) {
                // 默认假设为站点数据
                header.setDataType(1);
            }

            header.setDescription(extractDescription(headerLine));
        }

        return header;
    }

    /**
     * 从文件头中提取描述信息
     */
    private String extractDescription(String headerLine) {
        // 提取中文描述部分
        if (headerLine.contains("降水")) {
            return "降水数据";
        } else if (headerLine.contains("温度")) {
            return "温度数据";
        } else if (headerLine.contains("风")) {
            return "风场数据";
        } else {
            return "气象数据";
        }
    }

    /**
     * 检查字符串是否包含NULL字符
     */
    private boolean containsNullCharacters(String str) {
        if (str == null) return true;

        // 检查是否包含NULL字符或大量不可见字符
        int nullCount = 0;
        for (char c : str.toCharArray()) {
            if (c == '\0' || c == '\uFFFD') { // NULL字符或替换字符
                nullCount++;
            }
        }

        // 如果NULL字符超过字符串长度的10%，认为编码有问题
        return nullCount > str.length() * 0.1;
    }

    /**
     * 检测文件编码并读取首行
     *
     * @param file 文件对象
     * @return 包含编码和首行内容的数组 [encoding, firstLine]
     * @throws IOException 文件读取异常
     */
    private String[] detectEncodingAndReadFirstLine(File file) throws IOException {
        String[] encodings = {"GBK", "GB2312", "UTF-8", "ISO-8859-1"};

        for (String encoding : encodings) {
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(new FileInputStream(file), encoding))) {

                String firstLine = reader.readLine();
                if (firstLine != null && !containsNullCharacters(firstLine) && isValidMdfsHeader(firstLine)) {
                    log.info("检测到有效编码: {}, 首行: {}", encoding, firstLine.substring(0, Math.min(50, firstLine.length())));
                    return new String[]{encoding, firstLine};
                }
            } catch (Exception e) {
                log.debug("编码 {} 检测失败: {}", encoding, e.getMessage());
            }
        }

        throw new IOException("无法检测到有效的文件编码");
    }

    /**
     * 验证是否为有效的MDFS文件头
     */
    private boolean isValidMdfsHeader(String line) {
        if (line == null || line.trim().isEmpty()) {
            return false;
        }

        // MDFS文件头通常以"mdfs"开头，包含中文描述
        String lowerLine = line.toLowerCase();
        return lowerLine.startsWith("mdfs") ||
               line.contains("站") ||
               line.contains("降水") ||
               line.contains("surface") ||
               line.contains("grid");
    }

    /**
     * 解析第一类MDFS数据：站点数据
     */
    private MicapsType1Data parseType1MdfsData(File file, MdfsFileHeader header) throws IOException {
        log.info("开始解析第一类MDFS数据（站点数据）");

        try {
            // 使用专门的二进制解析器处理MDFS格式
            return binaryParser.parseStationBinaryData(file, header);
        } catch (Exception e) {
            log.error("解析MDFS站点数据失败，返回空数据结构", e);

            // 返回空数据结构，确保程序不会崩溃
            MicapsType1Data data = new MicapsType1Data();
            data.setDataType(1);
            data.setDescription(header.getDescription());
            data.setTotalStations(0);
            data.setStations(new ArrayList<>());
            return data;
        }
    }

    /**
     * 解析第四类MDFS数据：格点数据
     */
    private MicapsType4Data parseType4MdfsData(File file, MdfsFileHeader header) throws IOException {
        log.info("开始解析第四类MDFS数据（格点数据）");

        MicapsType4Data data = new MicapsType4Data();
        data.setDataType(4);
        data.setDescription(header.getDescription());

        // 由于MDFS格式复杂，这里先返回基本结构
        // 实际的二进制数据解析需要根据具体的MDFS格式规范实现
        data.setGridValues(new ArrayList<>());

        log.warn("MDFS二进制数据解析功能待实现，当前返回空数据结构");
        return data;
    }
    
    /**
     * 解析第一类数据：地面全要素填图数据
     */
    private MicapsType1Data parseType1Data(File file, String encoding) throws IOException {
        log.info("开始解析第一类数据，使用编码: {}", encoding);
        try (Scanner scanner = new Scanner(file, encoding)) {
            
            // 解析文件头
            String headerLine = scanner.nextLine().trim();
            String[] headerParts = headerLine.split("\\s+", 7);
            
            if (headerParts.length < 7) {
                throw new IllegalArgumentException("第一类数据文件头格式错误");
            }
            
            MicapsType1Data data = new MicapsType1Data();
            data.setDataType(Integer.parseInt(headerParts[1]));
            data.setDescription(headerParts[2]);
            data.setYear(Integer.parseInt(headerParts[3]));
            data.setMonth(Integer.parseInt(headerParts[4]));
            data.setDay(Integer.parseInt(headerParts[5]));
            data.setHour(Integer.parseInt(headerParts[6]));
            
            // 解析站点总数
            if (!scanner.hasNextLine()) {
                throw new IllegalArgumentException("缺少站点总数信息");
            }
            
            String stationCountLine = scanner.nextLine().trim();
            int totalStations = Integer.parseInt(stationCountLine);
            data.setTotalStations(totalStations);
            
            // 解析站点数据
            List<MicapsStation> stations = new ArrayList<>();
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine().trim();
                if (line.isEmpty()) continue;
                
                try {
                    MicapsStation station = parseStationLine(line);
                    if (station != null) {
                        stations.add(station);
                    }
                } catch (Exception e) {
                    System.err.println("解析站点数据失败，跳过该行: " + line + ", 错误: " + e.getMessage());
                }
            }
            
            data.setStations(stations);
            System.out.println("第一类数据解析完成，总站点数: " + totalStations + ", 实际解析: " + stations.size());
            
            return data;
        }
    }
    
    /**
     * 解析第四类数据：格点数据
     */
    private MicapsType4Data parseType4Data(File file, String encoding) throws IOException {
        log.info("开始解析第四类数据，使用编码: {}", encoding);
        try (Scanner scanner = new Scanner(file, encoding)) {
            
            // 解析文件头
            String headerLine = scanner.nextLine().trim();
            String[] headerParts = headerLine.split("\\s+");
            
            if (headerParts.length < 9) {
                throw new IllegalArgumentException("第四类数据文件头格式错误");
            }
            
            MicapsType4Data data = new MicapsType4Data();
            data.setDataType(Integer.parseInt(headerParts[1]));
            data.setDescription(headerParts[2]);
            data.setYear(Integer.parseInt(headerParts[3]));
            data.setMonth(Integer.parseInt(headerParts[4]));
            data.setDay(Integer.parseInt(headerParts[5]));
            data.setHour(Integer.parseInt(headerParts[6]));
            data.setForecastHour(Integer.parseInt(headerParts[7]));
            data.setLevel(Integer.parseInt(headerParts[8]));
            
            // 解析格点信息
            if (!scanner.hasNextLine()) {
                throw new IllegalArgumentException("缺少格点信息");
            }
            
            String gridInfoLine = scanner.nextLine().trim();
            String[] gridParts = gridInfoLine.split("\\s+");
            
            if (gridParts.length < 8) {
                throw new IllegalArgumentException("格点信息格式错误");
            }
            
            data.setLonInterval(Double.parseDouble(gridParts[0]));
            data.setLatInterval(Double.parseDouble(gridParts[1]));
            data.setStartLon(Double.parseDouble(gridParts[2]));
            data.setEndLon(Double.parseDouble(gridParts[3]));
            data.setStartLat(Double.parseDouble(gridParts[4]));
            data.setEndLat(Double.parseDouble(gridParts[5]));
            data.setLonGridNum(Integer.parseInt(gridParts[6]));
            data.setLatGridNum(Integer.parseInt(gridParts[7]));
            
            // 解析格点数据
            List<Double> gridValues = new ArrayList<>();
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine().trim();
                if (line.isEmpty()) continue;
                
                String[] values = line.split("\\s+");
                for (String value : values) {
                    try {
                        double val = Double.parseDouble(value);
                        gridValues.add(val);
                    } catch (NumberFormatException e) {
                        System.err.println("解析格点数值失败，跳过: " + value);
                    }
                }
            }
            
            data.setGridValues(gridValues);
            System.out.println("第四类数据解析完成，格点数: " + data.getLonGridNum() + "×" + data.getLatGridNum() +
                    " = " + (data.getLonGridNum() * data.getLatGridNum()) + ", 实际数据: " + gridValues.size());
            
            return data;
        }
    }
    
    /**
     * 解析站点数据行
     */
    private MicapsStation parseStationLine(String line) {
        String[] parts = line.split("\\s+");
        if (parts.length < 4) {
            return null;
        }
        
        MicapsStation station = new MicapsStation();
        
        try {
            station.setStationId(Long.parseLong(parts[0]));
            station.setLongitude(Double.parseDouble(parts[1]));
            station.setLatitude(Double.parseDouble(parts[2]));
            station.setElevation(Double.parseDouble(parts[3]));
            
            // 解析可选字段
            if (parts.length > 4) station.setTemperature(parseDoubleValue(parts[4]));
            if (parts.length > 5) station.setPressure(parseDoubleValue(parts[5]));
            if (parts.length > 6) station.setWindDirection(parseIntValue(parts[6]));
            if (parts.length > 7) station.setWindSpeed(parseIntValue(parts[7]));
            if (parts.length > 8) station.setPrecipitation6h(parseDoubleValue(parts[8]));
            if (parts.length > 9) station.setVisibility(parseDoubleValue(parts[9]));
            
            return station;
        } catch (NumberFormatException e) {
            System.err.println("解析站点数据失败: " + line);
            return null;
        }
    }
    
    /**
     * 解析双精度数值，处理缺值
     */
    private Double parseDoubleValue(String value) {
        try {
            double d = Double.parseDouble(value);
            return (d == 9999.0 || d == -9999.0) ? null : d;
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 解析整数数值，处理缺值
     */
    private Integer parseIntValue(String value) {
        try {
            int i = Integer.parseInt(value);
            return (i == 9999 || i == -9999) ? null : i;
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 获取指定区域内的站点
     */
    public List<MicapsStation> getStationsInRegion(MicapsData data, 
                                                   double minLon, double maxLon, 
                                                   double minLat, double maxLat) {
        if (!(data instanceof MicapsType1Data)) {
            return Collections.emptyList();
        }
        
        MicapsType1Data type1Data = (MicapsType1Data) data;
        return type1Data.getStationsInRange(minLon, maxLon, minLat, maxLat);
    }
    
    /**
     * 获取指定位置的格点数值
     */
    public Double getGridValueAtPosition(MicapsData data, double lon, double lat) {
        if (!(data instanceof MicapsType4Data)) {
            return null;
        }
        
        MicapsType4Data type4Data = (MicapsType4Data) data;
        return type4Data.getValueAtPosition(lon, lat);
    }
    
    /**
     * 获取有降水的站点
     */
    public List<MicapsStation> getStationsWithPrecipitation(MicapsData data) {
        if (!(data instanceof MicapsType1Data)) {
            return Collections.emptyList();
        }
        
        MicapsType1Data type1Data = (MicapsType1Data) data;
        if (type1Data.getStations() == null) {
            return Collections.emptyList();
        }
        
        return type1Data.getStations().stream()
                .filter(station -> station.getPrecipitation6h() != null && station.getPrecipitation6h() > 0.1)
                .collect(java.util.stream.Collectors.toList());
    }
}
