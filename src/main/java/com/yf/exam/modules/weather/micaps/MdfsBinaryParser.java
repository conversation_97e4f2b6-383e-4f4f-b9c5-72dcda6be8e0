package com.yf.exam.modules.weather.micaps;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.*;

/**
 * MDFS二进制数据解析器
 * 专门用于解析MICAPS MDFS文件的二进制数据部分
 * 
 * 参考：https://github.com/CyanideCN/micaps_mdfs
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Slf4j
@Component
public class MdfsBinaryParser {
    
    /**
     * 解析MDFS站点数据的二进制部分
     * 
     * @param file 文件对象
     * @param header 文件头信息
     * @return 站点数据
     * @throws IOException 读取异常
     */
    public MicapsType1Data parseStationBinaryData(File file, MdfsFileHeader header) throws IOException {
        log.info("开始解析MDFS站点数据的二进制部分");
        
        MicapsType1Data data = new MicapsType1Data();
        data.setDataType(1);
        data.setDescription(header.getDescription());
        
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            // 跳过文件头的文本部分
            long headerEndPos = findHeaderEndPosition(raf, header);
            raf.seek(headerEndPos);
            
            log.info("文件头结束位置: {} bytes", headerEndPos);
            
            // 读取二进制数据头部信息
            BinaryDataHeader binaryHeader = readBinaryHeader(raf);
            log.info("二进制数据头: {}", binaryHeader);
            
            // 读取站点数据
            List<MicapsStation> stations = readStationData(raf, binaryHeader);
            
            data.setTotalStations(stations.size());
            data.setStations(stations);
            
            log.info("成功解析 {} 个站点的数据", stations.size());
            return data;
            
        } catch (Exception e) {
            log.error("解析MDFS站点数据失败", e);
            
            // 返回空数据结构，避免程序崩溃
            data.setTotalStations(0);
            data.setStations(new ArrayList<>());
            return data;
        }
    }
    
    /**
     * 查找文件头结束位置
     * 使用MdfsFileHeader中已经计算好的数据开始位置
     */
    private long findHeaderEndPosition(RandomAccessFile raf, MdfsFileHeader header) throws IOException {
        // 使用已经计算好的数据开始位置
        long dataStartOffset = header.getDataStartOffset();

        if (dataStartOffset > 0 && dataStartOffset < raf.length()) {
            log.debug("使用预计算的数据开始位置: {}", dataStartOffset);
            return dataStartOffset;
        }

        // 如果没有预计算的位置，使用原来的方法
        raf.seek(0);

        // 读取文件头行
        String headerLine = readTextLine(raf, header.getEncoding());

        // 文件头通常只有一行，后面就是二进制数据
        long pos = raf.getFilePointer();

        // 尝试跳过可能的填充字节
        while (pos < raf.length()) {
            int b = raf.read();
            if (b == -1) break;

            // 如果遇到非文本字符，认为是二进制数据开始
            if (b < 32 && b != 10 && b != 13 && b != 9) {
                raf.seek(pos);
                break;
            }
            pos++;
        }

        log.debug("动态计算的数据开始位置: {}", pos);
        return pos;
    }
    
    /**
     * 读取文本行（指定编码）
     */
    private String readTextLine(RandomAccessFile raf, String encoding) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        int b;
        
        while ((b = raf.read()) != -1) {
            if (b == '\n') break;
            if (b == '\r') {
                // 检查是否为\r\n
                long pos = raf.getFilePointer();
                if (raf.read() != '\n') {
                    raf.seek(pos);
                }
                break;
            }
            baos.write(b);
        }
        
        return new String(baos.toByteArray(), encoding);
    }
    
    /**
     * 读取二进制数据头部
     */
    private BinaryDataHeader readBinaryHeader(RandomAccessFile raf) throws IOException {
        BinaryDataHeader header = new BinaryDataHeader();
        
        // 读取基本信息（根据MDFS格式规范）
        // 注意：这里的格式需要根据实际的MDFS规范调整
        
        byte[] buffer = new byte[64]; // 假设头部信息不超过64字节
        int bytesRead = raf.read(buffer);
        
        if (bytesRead < 16) {
            throw new IOException("二进制数据头部信息不完整");
        }
        
        ByteBuffer bb = ByteBuffer.wrap(buffer);
        bb.order(ByteOrder.LITTLE_ENDIAN); // MDFS通常使用小端序
        
        // 这里需要根据实际的MDFS格式规范来解析
        // 暂时使用简化的解析逻辑
        header.setStationCount(Math.abs(bb.getInt(0)) % 10000); // 防止异常值
        header.setDataLength(Math.abs(bb.getInt(4)) % 1000000);
        
        log.debug("解析二进制头部: 站点数={}, 数据长度={}", 
            header.getStationCount(), header.getDataLength());
        
        return header;
    }
    
    /**
     * 读取站点数据
     */
    private List<MicapsStation> readStationData(RandomAccessFile raf, BinaryDataHeader binaryHeader) throws IOException {
        List<MicapsStation> stations = new ArrayList<>();
        
        // 由于MDFS格式复杂且缺乏详细规范，这里实现简化版本
        // 实际应用中需要根据具体的MDFS格式文档来实现
        
        int maxStations = Math.min(binaryHeader.getStationCount(), 1000); // 限制最大站点数
        
        for (int i = 0; i < maxStations && raf.getFilePointer() < raf.length() - 20; i++) {
            try {
                MicapsStation station = readSingleStation(raf);
                if (station != null && isValidStation(station)) {
                    stations.add(station);
                }
            } catch (Exception e) {
                log.debug("读取第{}个站点数据失败: {}", i + 1, e.getMessage());
                break; // 遇到错误就停止读取
            }
        }
        
        return stations;
    }
    
    /**
     * 读取单个站点数据
     */
    private MicapsStation readSingleStation(RandomAccessFile raf) throws IOException {
        if (raf.getFilePointer() >= raf.length() - 20) {
            return null;
        }

        MicapsStation station = new MicapsStation();

        byte[] buffer = new byte[20]; // 假设每个站点数据20字节
        int bytesRead = raf.read(buffer);

        if (bytesRead < 20) {
            return null;
        }

        ByteBuffer bb = ByteBuffer.wrap(buffer);
        bb.order(ByteOrder.LITTLE_ENDIAN);

        // 简化的站点数据解析（需要根据实际格式调整）
        try {
            int stationId = bb.getInt(0);
            float longitude = bb.getFloat(4);
            float latitude = bb.getFloat(8);
            float value = bb.getFloat(12);

            // 数据有效性检查
            if (Math.abs(longitude) <= 180 && Math.abs(latitude) <= 90 &&
                stationId > 0 && stationId < 999999) {

                station.setStationId(stationId);
                station.setLongitude(longitude);
                station.setLatitude(latitude);

                // 根据数据类型设置相应的值（这里假设是降水数据）
                if (value >= 0 && value < 1000) {
                    station.setPrecipitation6h((double) value);
                }

                return station;
            }
        } catch (Exception e) {
            log.debug("解析站点数据异常: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 验证站点数据是否有效
     */
    private boolean isValidStation(MicapsStation station) {
        return station != null &&
               station.getStationId() > 0 &&
               Math.abs(station.getLongitude()) <= 180 &&
               Math.abs(station.getLatitude()) <= 90;
    }
    
    /**
     * 二进制数据头部信息
     */
    private static class BinaryDataHeader {
        private int stationCount;
        private int dataLength;
        
        public int getStationCount() { return stationCount; }
        public void setStationCount(int stationCount) { this.stationCount = stationCount; }
        public int getDataLength() { return dataLength; }
        public void setDataLength(int dataLength) { this.dataLength = dataLength; }
        
        @Override
        public String toString() {
            return String.format("BinaryDataHeader[stations=%d, length=%d]", stationCount, dataLength);
        }
    }
}
