package com.yf.exam.modules.weather.scoring.controller;

import com.yf.exam.core.api.ApiRest;
import com.yf.exam.modules.weather.scoring.dto.PrecipitationScoringResult;
import com.yf.exam.modules.weather.scoring.service.PrecipitationAreaScoringService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 降水落区评分控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Slf4j
@RestController
@RequestMapping("/weather/scoring/precipitation")
@Api(tags = "降水落区评分")
public class PrecipitationScoringController {

    @Autowired
    private PrecipitationAreaScoringService precipitationAreaScoringService;

    /**
     * 计算降水落区评分
     */
    @PostMapping("/calculate")
    @ApiOperation("计算降水落区评分")
    public ApiRest<PrecipitationScoringResult> calculatePrecipitationScore(
            @ApiParam("实况降水文件路径") @RequestParam String actualFilePath,
            @ApiParam("CMA-MESO文件路径") @RequestParam String cmaMesoFilePath,
            @ApiParam("考生答案") @RequestBody Map<String, Object> studentAnswer) {
        
        try {
            log.info("开始计算降水落区评分，实况文件：{}，CMA文件：{}", actualFilePath, cmaMesoFilePath);
            
            PrecipitationScoringResult result = precipitationAreaScoringService.calculatePrecipitationScore(
                actualFilePath, cmaMesoFilePath, studentAnswer);
            
            if (result.isSuccess()) {
                log.info("降水落区评分计算成功，得分：{}", result.getFinalScore());
                return ApiRest.ok(result, "降水落区评分计算成功");
            } else {
                log.warn("降水落区评分计算失败：{}", result.getMessage());
                return ApiRest.fail(result.getMessage());
            }
            
        } catch (Exception e) {
            log.error("降水落区评分计算异常", e);
            return ApiRest.fail("降水落区评分计算异常：" + e.getMessage());
        }
    }

    /**
     * 测试降水落区评分（使用示例数据）
     */
    @GetMapping("/test")
    @ApiOperation("测试降水落区评分")
    public ApiRest<PrecipitationScoringResult> testPrecipitationScoring() {
        try {
            // 创建测试用的考生答案
            Map<String, Object> testAnswer = createTestStudentAnswer();
            
            // 使用示例文件进行测试
            String actualFilePath = "sample_actual.000";  // 实况文件
            String cmaMesoFilePath = "sample_cma_meso.004"; // CMA-MESO文件
            
            PrecipitationScoringResult result = precipitationAreaScoringService.calculatePrecipitationScore(
                actualFilePath, cmaMesoFilePath, testAnswer);
            
            return ApiRest.ok(result, "测试完成");

        } catch (Exception e) {
            log.error("测试降水落区评分异常", e);
            return ApiRest.fail("测试异常：" + e.getMessage());
        }
    }

    /**
     * 创建测试用的考生答案
     */
    private Map<String, Object> createTestStudentAnswer() {
        // 这里创建一个示例的降水落区答案
        // 实际使用时，这个数据来自前端用户绘制的降水落区
        return Map.of(
            "content", Map.of(
                "小雨", java.util.List.of(
                    Map.of(
                        "geometry", Map.of(
                            "type", "Polygon",
                            "coordinates", java.util.List.of(
                                java.util.List.of(
                                    java.util.List.of(116.0, 39.5),
                                    java.util.List.of(117.0, 39.5),
                                    java.util.List.of(117.0, 40.5),
                                    java.util.List.of(116.0, 40.5),
                                    java.util.List.of(116.0, 39.5)
                                )
                            )
                        )
                    )
                ),
                "中雨", java.util.List.of(
                    Map.of(
                        "geometry", Map.of(
                            "type", "Polygon",
                            "coordinates", java.util.List.of(
                                java.util.List.of(
                                    java.util.List.of(116.5, 40.0),
                                    java.util.List.of(117.5, 40.0),
                                    java.util.List.of(117.5, 41.0),
                                    java.util.List.of(116.5, 41.0),
                                    java.util.List.of(116.5, 40.0)
                                )
                            )
                        )
                    )
                )
            )
        );
    }
}
